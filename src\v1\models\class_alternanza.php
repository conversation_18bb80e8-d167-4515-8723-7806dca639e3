<?php
class Alternanza {
    private $user = null; // username

    /**
     * @param Auth $user oggetto autenticazione
     */
    public function __construct($user) {
        /*{{{ */
        // Inizializzo l'oggetto user
        $this->user = $user;
        /*}}}*/
    }

    /**
     * Funzione in GET
     * Recupera l'elenco dei progetti disponibili per uno studente
     * I parametri possibili sono:
     * $dati['id_studente'] - ID dello studente (obbligatorio)
     * $dati['format'] - Formato della risposta (default: json)
     * @param array $dati
     * @return array Elenco dei progetti disponibili
     */
    public function getProgettiDisponibili($dati) {
        /*{{{ */
        $errori = [];
        $results = [];

        // Verifica dei parametri obbligatori
        if (!isset($dati['id_studente']) || intval($dati['id_studente']) <= 0) {
            $errori[] = "ID studente mancante o non valido";
        }

        if (count($errori) == 0) {
            // Prepara la chiamata all'API esterna
            $id_scuola = $this->user->data->GetParametroMastercom('MASTERCOM_ID');
            $url = "https://alternanza.registroelettronico.com/{$id_scuola}/api/progetti/?format=json&id_studente=" . $dati['id_studente'];
            
            // Recupera le credenziali dal file di configurazione
            $username = $this->user->data->GetParametroMastercom('MASTERSTAGE_USERNAME');
            $password = $this->user->data->GetParametroMastercom('MASTERSTAGE_PASSWORD');
            
            // Esegue la chiamata all'API esterna
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $username . ":" . $password);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                $errori[] = "Errore nella chiamata API: " . curl_error($ch);
            } elseif ($http_code != 200) {
                $errori[] = "Errore nella risposta API: codice HTTP " . $http_code;
            } else {
                $results = json_decode($response, true);
                
                // Aggiunge il campo 'prenotabile' = true per tutti i progetti
                if (is_array($results)) {
                    foreach ($results as &$progetto) {
                        $progetto['prenotabile'] = true;
                    }
                }
            }
            
            curl_close($ch);
        }

        if (count($errori) > 0) {
            return ['errori' => $errori];
        }

        return $results;
        /*}}}*/
    }

    /**
     * Funzione in GET
     * Recupera l'elenco degli stage (progetti a cui lo studente è iscritto)
     * I parametri possibili sono:
     * $dati['id_studente'] - ID dello studente (obbligatorio)
     * $dati['format'] - Formato della risposta (default: json)
     * @param array $dati
     * @return array Elenco degli stage
     */
    public function getStage($dati) {
        /*{{{ */
        $errori = [];
        $results = [];

        // Verifica dei parametri obbligatori
        if (!isset($dati['id_studente']) || intval($dati['id_studente']) <= 0) {
            $errori[] = "ID studente mancante o non valido";
        }

        if (count($errori) == 0) {
            // Prepara la chiamata all'API esterna
            $id_scuola = $this->user->data->GetParametroMastercom('MASTERCOM_ID');
            $url = "https://alternanza.registroelettronico.com/{$id_scuola}/api/stage/?format=json&id_studente=" . $dati['id_studente'];
            
            // Recupera le credenziali dal file di configurazione
            $username = $this->user->data->GetParametroMastercom('MASTERSTAGE_USERNAME');
            $password = $this->user->data->GetParametroMastercom('MASTERSTAGE_PASSWORD');
            
            // Esegue la chiamata all'API esterna
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $username . ":" . $password);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                $errori[] = "Errore nella chiamata API: " . curl_error($ch);
            } elseif ($http_code != 200) {
                $errori[] = "Errore nella risposta API: codice HTTP " . $http_code;
            } else {
                $results = json_decode($response, true);
                
                // Aggiunge il campo 'prenotabile' = false per tutti gli stage
                if (is_array($results)) {
                    foreach ($results as &$stage) {
                        $stage['prenotabile'] = false;
                    }
                }
            }
            
            curl_close($ch);
        }

        if (count($errori) > 0) {
            return ['errori' => $errori];
        }

        return $results;
        /*}}}*/
    }

    /**
     * Funzione in POST
     * Iscrive uno studente a un progetto
     * I parametri possibili sono:
     * $dati['id_studente'] - ID dello studente (obbligatorio)
     * $dati['id_progetto'] - ID del progetto (obbligatorio)
     * @param array $dati
     * @return array Risultato dell'operazione
     */
    public function iscrizioneProgetto($dati) {
        /*{{{ */
        $errori = [];
        $results = [];

        // Verifica dei parametri obbligatori
        if (!isset($dati['id_studente']) || intval($dati['id_studente']) <= 0) {
            $errori[] = "ID studente mancante o non valido";
        }
        
        if (!isset($dati['id_progetto']) || intval($dati['id_progetto']) <= 0) {
            $errori[] = "ID progetto mancante o non valido";
        }

        if (count($errori) == 0) {
            // Prepara la chiamata all'API esterna
            $id_scuola = $this->user->data->GetParametroMastercom('MASTERCOM_ID');
            $url = "https://alternanza.registroelettronico.com/{$id_scuola}/api/stage/";
            
            // Recupera le credenziali dal file di configurazione
            $username = $this->user->data->GetParametroMastercom('MASTERSTAGE_USERNAME');
            $password = $this->user->data->GetParametroMastercom('MASTERSTAGE_PASSWORD');
            
            // Prepara i dati da inviare
            $payload = [
                'id_studente' => intval($dati['id_studente']),
                'id_progetto' => intval($dati['id_progetto'])
            ];
            
            // Esegue la chiamata all'API esterna
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $username . ":" . $password);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                $errori[] = "Errore nella chiamata API: " . curl_error($ch);
            } elseif ($http_code != 200) {
                $errori[] = "Errore nella risposta API: codice HTTP " . $http_code;
            } else {
                $results = [
                    'esito' => 'OK',
                    'messaggio' => 'Iscrizione completata con successo'
                ];
            }
            
            curl_close($ch);
        }

        if (count($errori) > 0) {
            return [
                'esito' => 'KO',
                'errori' => $errori
            ];
        }

        return $results;
        /*}}}*/
    }

    /**
     * Funzione in DELETE
     * Cancella l'iscrizione di uno studente a uno stage
     * I parametri possibili sono:
     * $dati['id_studente'] - ID dello studente (obbligatorio)
     * $dati['id_stage'] - ID dello stage (obbligatorio)
     * @param array $dati
     * @return array Risultato dell'operazione
     */
    public function disiscrizione($dati) {
        /*{{{ */
        $errori = [];
        $results = [];

        // Verifica dei parametri obbligatori
        if (!isset($dati['id_studente']) || intval($dati['id_studente']) <= 0) {
            $errori[] = "ID studente mancante o non valido";
        }
        
        if (!isset($dati['id_stage']) || intval($dati['id_stage']) <= 0) {
            $errori[] = "ID stage mancante o non valido";
        }

        if (count($errori) == 0) {
            // Prepara la chiamata all'API esterna
            $id_scuola = $this->user->data->GetParametroMastercom('MASTERCOM_ID');
            $url = "https://alternanza.registroelettronico.com/{$id_scuola}/api/stage/" . $dati['id_stage'] . "/?format=json&id_studente=" . $dati['id_studente'];
            
            // Recupera le credenziali dal file di configurazione
            $username = $this->user->data->GetParametroMastercom('MASTERSTAGE_USERNAME');
            $password = $this->user->data->GetParametroMastercom('MASTERSTAGE_PASSWORD');
            
            // Esegue la chiamata all'API esterna
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $username . ":" . $password);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                $errori[] = "Errore nella chiamata API: " . curl_error($ch);
            } elseif ($http_code != 200) {
                $errori[] = "Errore nella risposta API: codice HTTP " . $http_code;
            } else {
                $results = [
                    'esito' => 'OK',
                    'messaggio' => 'Disiscrizione completata con successo'
                ];
            }
            
            curl_close($ch);
        }

        if (count($errori) > 0) {
            return [
                'esito' => 'KO',
                'errori' => $errori
            ];
        }

        return $results;
        /*}}}*/
    }
}
?>
