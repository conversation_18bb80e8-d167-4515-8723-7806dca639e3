#!/usr/bin/php
<?php
require_once "../models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom

if (count($argv) < 2) {
    echo "restore_previous_revision.php <id1,id2,id3,...> [db]\n";
    echo "Ripristina i documenti specificati alla revisione precedente\n";
    echo "db: default mastercom. Specifica il database CouchDB da utilizzare\n";
    exit;
}

$ids = explode(',', $argv[1]);
$db = isset($argv[2]) ? $argv[2] : 'mastercom';

if (!(in_array($db, ['mastercom', 'calendar', 'archive', 'documents']))) {
    $db = 'mastercom';
}

$data = new Data;
if (true) {
    $previous = $data->SetCdb($db);
    
    if ($previous !== false) {
        foreach ($ids as $id) {
            echo "Elaborazione $id: ";
            
            // Ottieni il documento corrente
            $current_doc = $data->cdb->call($id);
            if (!isset($current_doc['_id'])) {
                echo "documento non trovato\n";
                continue;
            }

            // Ottieni la lista delle revisioni
            $revs = $data->cdb->call($id . '?revs=true&revs_info=true');
            if (!isset($revs['_revs_info']) || count($revs['_revs_info']) < 2) {
                echo "nessuna revisione precedente disponibile\n";
                continue;
            }

            // Trova la revisione precedente disponibile
            $prev_rev = null;
            foreach ($revs['_revs_info'] as $rev_info) {
                if ($rev_info['status'] === 'available' && $rev_info['rev'] !== $current_doc['_rev']) {
                    $prev_rev = $rev_info['rev'];
                    break;
                }
            }

            if (!$prev_rev) {
                echo "nessuna revisione precedente disponibile\n";
                continue;
            }

            // Ottieni il documento della revisione precedente
            $prev_doc = $data->cdb->call($id . '?rev=' . $prev_rev);
            if (!isset($prev_doc['_id'])) {
                echo "errore nel recupero della revisione precedente\n";
                continue;
            }

            // Aggiorna il documento con la revisione corrente
            $prev_doc['_rev'] = $current_doc['_rev'];
            
            // Salva il documento
            $result = $data->cdb->call($id, 'PUT', $prev_doc);
            if ($result['ok']) {
                echo "ripristinato alla revisione " . $prev_rev . "\n";
            } else {
                echo "errore durante il ripristino: ";
                print_r($result);
                echo "\n";
            }
        }
    } else {
        echo "Errore nella connessione al database\n";
    }
} else {
    echo "scripts blocked\n";
}