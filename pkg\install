#!/bin/sh
grep -qxF 'sudo su - root' /home/<USER>/.bashrc || echo 'sudo su - root' >> /home/<USER>/.bashrc ;

sed -i "s/error_reporting = E_ALL \& \~E_DEPRECATED \& \~E_STRICT$/error_reporting = E_ALL \& \~E_DEPRECATED \& \~E_STRICT \& \~E_NOTICE/" /etc/php/7.0/cli/php.ini ;

grep -qxF 'alias goscript="cd /var/www-source/next-api/v1/scripts"' /root/.bashrc || echo 'alias goscript="cd /var/www-source/next-api/v1/scripts"' >> /root/.bashrc ;
grep -qxF 'alias goconf="cd /var/www-source/next-api/v1/configs"' /root/.bashrc || echo 'alias goconf="cd /var/www-source/next-api/v1/configs"' >> /root/.bashrc ;
grep -qxF 'alias gopreisc="cd /var/www-source/preiscrizioni/"' /root/.bashrc || echo 'alias gopreisc="cd /var/www-source/preiscrizioni"' >> /root/.bashrc ;
grep -qxF 'function cedit() { (cd /var/www-source/next-api/v1/scripts && php edit_record.php "$1" ) }' /root/.bashrc || echo 'function cedit() { (cd /var/www-source/next-api/v1/scripts && php edit_record.php "$1" ) }' >> /root/.bashrc ;
grep -qxF 'function cbackup() { (cd /var/www-source/next-api/v1/scripts && php backup_db.php "$1" ) }' /root/.bashrc || echo 'function cbackup() { (cd /var/www-source/next-api/v1/scripts && php backup_db.php "$1" ) }' >> /root/.bashrc ;
grep -qxF 'function template_manager() { (cd /var/www-source/next-api/v1/scripts && php template_manager.php "$1") }' /root/.bashrc || echo 'function template_manager() { (cd /var/www-source/next-api/v1/scripts && php template_manager.php "$1" ) }' >> /root/.bashrc ;
grep -qxF 'function c_manager() { (cd /var/www-source/next-api/v1/scripts && php couch_manager.php "$1") }' /root/.bashrc || echo 'function c_manager() { (cd /var/www-source/next-api/v1/scripts && php couch_manager.php "$1" ) }' >> /root/.bashrc ;
grep -qxF 'function checkout-status() { (cd /var/lib/checkout/next-api && echo "next-api:" && git status) && (cd /var/lib/checkout/preiscrizioni && echo "preiscrizioni:" && git status) && (cd /var/lib/checkout/next-assets && echo "next-assets:" && git status) }' /root/.bashrc || echo 'function checkout-status() { (cd /var/lib/checkout/next-api && echo "next-api:" && git status) && (cd /var/lib/checkout/preiscrizioni && echo "preiscrizioni:" && git status) && (cd /var/lib/checkout/next-assets && echo "next-assets:" && git status) }' >> /root/.bashrc ;
grep -qxF 'function init_db() { (cd /var/www-source/next-api/v1/configs && php init_db.php "$1") }' /root/.bashrc || echo 'function init_db() { (cd /var/www-source/next-api/v1/configs && php init_db.php "$1" ) }' >> /root/.bashrc ;


PHP=`which php`
RESFILES=`$PHP /var/lib/checkout/next-api/src/v1/configs/init_files.php`
echo $RESFILES
if [ "$RESFILES" = "OK" ]; then
	cd /var/lib/checkout/next-api/src/v1/configs/
	RESDB=`$PHP init_db.php "auto"`
	$PHP /var/lib/checkout/next-api/src/v1/configs/postinit.php $RESDB
fi


