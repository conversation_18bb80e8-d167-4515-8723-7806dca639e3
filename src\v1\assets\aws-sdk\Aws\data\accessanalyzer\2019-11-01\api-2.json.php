<?php
// This file was auto-generated from sdk-root/src/data/accessanalyzer/2019-11-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-11-01', 'endpointPrefix' => 'access-analyzer', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Access Analyzer', 'serviceId' => 'AccessAnalyzer', 'signatureVersion' => 'v4', 'signingName' => 'access-analyzer', 'uid' => 'accessanalyzer-2019-11-01', ], 'operations' => [ 'ApplyArchiveRule' => [ 'name' => 'ApplyArchiveRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/archive-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ApplyArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CancelPolicyGeneration' => [ 'name' => 'CancelPolicyGeneration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policy/generation/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelPolicyGenerationRequest', ], 'output' => [ 'shape' => 'CancelPolicyGenerationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateAccessPreview' => [ 'name' => 'CreateAccessPreview', 'http' => [ 'method' => 'PUT', 'requestUri' => '/access-preview', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAccessPreviewRequest', ], 'output' => [ 'shape' => 'CreateAccessPreviewResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateAnalyzer' => [ 'name' => 'CreateAnalyzer', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAnalyzerRequest', ], 'output' => [ 'shape' => 'CreateAnalyzerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateArchiveRule' => [ 'name' => 'CreateArchiveRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer/{analyzerName}/archive-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAnalyzer' => [ 'name' => 'DeleteAnalyzer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/analyzer/{analyzerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAnalyzerRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteArchiveRule' => [ 'name' => 'DeleteArchiveRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/analyzer/{analyzerName}/archive-rule/{ruleName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetAccessPreview' => [ 'name' => 'GetAccessPreview', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-preview/{accessPreviewId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccessPreviewRequest', ], 'output' => [ 'shape' => 'GetAccessPreviewResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAnalyzedResource' => [ 'name' => 'GetAnalyzedResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzed-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnalyzedResourceRequest', ], 'output' => [ 'shape' => 'GetAnalyzedResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAnalyzer' => [ 'name' => 'GetAnalyzer', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer/{analyzerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnalyzerRequest', ], 'output' => [ 'shape' => 'GetAnalyzerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetArchiveRule' => [ 'name' => 'GetArchiveRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer/{analyzerName}/archive-rule/{ruleName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetArchiveRuleRequest', ], 'output' => [ 'shape' => 'GetArchiveRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFinding' => [ 'name' => 'GetFinding', 'http' => [ 'method' => 'GET', 'requestUri' => '/finding/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingRequest', ], 'output' => [ 'shape' => 'GetFindingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetGeneratedPolicy' => [ 'name' => 'GetGeneratedPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy/generation/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGeneratedPolicyRequest', ], 'output' => [ 'shape' => 'GetGeneratedPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccessPreviewFindings' => [ 'name' => 'ListAccessPreviewFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/access-preview/{accessPreviewId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessPreviewFindingsRequest', ], 'output' => [ 'shape' => 'ListAccessPreviewFindingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccessPreviews' => [ 'name' => 'ListAccessPreviews', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-preview', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessPreviewsRequest', ], 'output' => [ 'shape' => 'ListAccessPreviewsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAnalyzedResources' => [ 'name' => 'ListAnalyzedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/analyzed-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnalyzedResourcesRequest', ], 'output' => [ 'shape' => 'ListAnalyzedResourcesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAnalyzers' => [ 'name' => 'ListAnalyzers', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnalyzersRequest', ], 'output' => [ 'shape' => 'ListAnalyzersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListArchiveRules' => [ 'name' => 'ListArchiveRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer/{analyzerName}/archive-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListArchiveRulesRequest', ], 'output' => [ 'shape' => 'ListArchiveRulesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/finding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPolicyGenerations' => [ 'name' => 'ListPolicyGenerations', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy/generation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPolicyGenerationsRequest', ], 'output' => [ 'shape' => 'ListPolicyGenerationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartPolicyGeneration' => [ 'name' => 'StartPolicyGeneration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policy/generation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartPolicyGenerationRequest', ], 'output' => [ 'shape' => 'StartPolicyGenerationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StartResourceScan' => [ 'name' => 'StartResourceScan', 'http' => [ 'method' => 'POST', 'requestUri' => '/resource/scan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartResourceScanRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateArchiveRule' => [ 'name' => 'UpdateArchiveRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer/{analyzerName}/archive-rule/{ruleName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateFindings' => [ 'name' => 'UpdateFindings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/finding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFindingsRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'ValidatePolicy' => [ 'name' => 'ValidatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/validation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ValidatePolicyRequest', ], 'output' => [ 'shape' => 'ValidatePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessPointArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:s3:[^:]*:[^:]*:accesspoint/.*', ], 'AccessPointPolicy' => [ 'type' => 'string', ], 'AccessPreview' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'configurations', 'createdAt', 'id', 'status', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'configurations' => [ 'shape' => 'ConfigurationsMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'AccessPreviewId', ], 'status' => [ 'shape' => 'AccessPreviewStatus', ], 'statusReason' => [ 'shape' => 'AccessPreviewStatusReason', ], ], ], 'AccessPreviewFinding' => [ 'type' => 'structure', 'required' => [ 'changeType', 'createdAt', 'id', 'resourceOwnerAccount', 'resourceType', 'status', ], 'members' => [ 'action' => [ 'shape' => 'ActionList', ], 'changeType' => [ 'shape' => 'FindingChangeType', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'String', ], 'existingFindingId' => [ 'shape' => 'FindingId', ], 'existingFindingStatus' => [ 'shape' => 'FindingStatus', ], 'id' => [ 'shape' => 'AccessPreviewFindingId', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'resource' => [ 'shape' => 'String', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'status' => [ 'shape' => 'FindingStatus', ], ], ], 'AccessPreviewFindingId' => [ 'type' => 'string', ], 'AccessPreviewFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPreviewFinding', ], ], 'AccessPreviewId' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'AccessPreviewStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'CREATING', 'FAILED', ], ], 'AccessPreviewStatusReason' => [ 'type' => 'structure', 'required' => [ 'code', ], 'members' => [ 'code' => [ 'shape' => 'AccessPreviewStatusReasonCode', ], ], ], 'AccessPreviewStatusReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'INVALID_CONFIGURATION', ], ], 'AccessPreviewSummary' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'createdAt', 'id', 'status', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'AccessPreviewId', ], 'status' => [ 'shape' => 'AccessPreviewStatus', ], 'statusReason' => [ 'shape' => 'AccessPreviewStatusReason', ], ], ], 'AccessPreviewsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPreviewSummary', ], ], 'AclCanonicalId' => [ 'type' => 'string', ], 'AclGrantee' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'AclCanonicalId', ], 'uri' => [ 'shape' => 'AclUri', ], ], 'union' => true, ], 'AclPermission' => [ 'type' => 'string', 'enum' => [ 'READ', 'WRITE', 'READ_ACP', 'WRITE_ACP', 'FULL_CONTROL', ], ], 'AclUri' => [ 'type' => 'string', ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AnalyzedResource' => [ 'type' => 'structure', 'required' => [ 'analyzedAt', 'createdAt', 'isPublic', 'resourceArn', 'resourceOwnerAccount', 'resourceType', 'updatedAt', ], 'members' => [ 'actions' => [ 'shape' => 'ActionList', ], 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'String', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'sharedVia' => [ 'shape' => 'SharedViaList', ], 'status' => [ 'shape' => 'FindingStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AnalyzedResourceSummary' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'resourceOwnerAccount', 'resourceType', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], ], 'AnalyzedResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyzedResourceSummary', ], ], 'AnalyzerArn' => [ 'type' => 'string', 'pattern' => '[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:analyzer/.{1,255}', ], 'AnalyzerStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'DISABLED', 'FAILED', ], ], 'AnalyzerSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'name', 'status', 'type', ], 'members' => [ 'arn' => [ 'shape' => 'AnalyzerArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastResourceAnalyzed' => [ 'shape' => 'String', ], 'lastResourceAnalyzedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'AnalyzerStatus', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'tags' => [ 'shape' => 'TagsMap', ], 'type' => [ 'shape' => 'Type', ], ], ], 'AnalyzersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyzerSummary', ], ], 'ApplyArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'ruleName', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'ruleName' => [ 'shape' => 'Name', ], ], ], 'ArchiveRuleSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'filter', 'ruleName', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'ruleName' => [ 'shape' => 'Name', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ArchiveRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArchiveRuleSummary', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelPolicyGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'CancelPolicyGenerationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CloudTrailArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:cloudtrail:[^:]*:[^:]*:trail/.{1,576}', ], 'CloudTrailDetails' => [ 'type' => 'structure', 'required' => [ 'accessRole', 'startTime', 'trails', ], 'members' => [ 'accessRole' => [ 'shape' => 'RoleArn', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'trails' => [ 'shape' => 'TrailList', ], ], ], 'CloudTrailProperties' => [ 'type' => 'structure', 'required' => [ 'endTime', 'startTime', 'trailProperties', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'trailProperties' => [ 'shape' => 'TrailPropertiesList', ], ], ], 'ConditionKeyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'iamRole' => [ 'shape' => 'IamRoleConfiguration', ], 'kmsKey' => [ 'shape' => 'KmsKeyConfiguration', ], 's3Bucket' => [ 'shape' => 'S3BucketConfiguration', ], 'secretsManagerSecret' => [ 'shape' => 'SecretsManagerSecretConfiguration', ], 'sqsQueue' => [ 'shape' => 'SqsQueueConfiguration', ], ], 'union' => true, ], 'ConfigurationsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigurationsMapKey', ], 'value' => [ 'shape' => 'Configuration', ], ], 'ConfigurationsMapKey' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAccessPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'configurations', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'configurations' => [ 'shape' => 'ConfigurationsMap', ], ], ], 'CreateAccessPreviewResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'AccessPreviewId', ], ], ], 'CreateAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'type', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', ], 'archiveRules' => [ 'shape' => 'InlineArchiveRulesList', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagsMap', ], 'type' => [ 'shape' => 'Type', ], ], ], 'CreateAnalyzerResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AnalyzerArn', ], ], ], 'CreateArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'filter', 'ruleName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'ruleName' => [ 'shape' => 'Name', ], ], ], 'Criterion' => [ 'type' => 'structure', 'members' => [ 'contains' => [ 'shape' => 'ValueList', ], 'eq' => [ 'shape' => 'ValueList', ], 'exists' => [ 'shape' => 'Boolean', ], 'neq' => [ 'shape' => 'ValueList', ], ], ], 'DeleteAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'ruleName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'ruleName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'FilterCriteriaMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Criterion', ], ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'analyzedAt', 'condition', 'createdAt', 'id', 'resourceOwnerAccount', 'resourceType', 'status', 'updatedAt', ], 'members' => [ 'action' => [ 'shape' => 'ActionList', ], 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'FindingId', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'resource' => [ 'shape' => 'String', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'status' => [ 'shape' => 'FindingStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'FindingChangeType' => [ 'type' => 'string', 'enum' => [ 'CHANGED', 'NEW', 'UNCHANGED', ], ], 'FindingId' => [ 'type' => 'string', ], 'FindingIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingId', ], ], 'FindingSource' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'detail' => [ 'shape' => 'FindingSourceDetail', ], 'type' => [ 'shape' => 'FindingSourceType', ], ], ], 'FindingSourceDetail' => [ 'type' => 'structure', 'members' => [ 'accessPointArn' => [ 'shape' => 'String', ], ], ], 'FindingSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingSource', ], ], 'FindingSourceType' => [ 'type' => 'string', 'enum' => [ 'POLICY', 'BUCKET_ACL', 'S3_ACCESS_POINT', ], ], 'FindingStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', 'RESOLVED', ], ], 'FindingStatusUpdate' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'FindingSummary' => [ 'type' => 'structure', 'required' => [ 'analyzedAt', 'condition', 'createdAt', 'id', 'resourceOwnerAccount', 'resourceType', 'status', 'updatedAt', ], 'members' => [ 'action' => [ 'shape' => 'ActionList', ], 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'FindingId', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'resource' => [ 'shape' => 'String', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'status' => [ 'shape' => 'FindingStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'FindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingSummary', ], ], 'GeneratedPolicy' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'String', ], ], ], 'GeneratedPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeneratedPolicy', ], ], 'GeneratedPolicyProperties' => [ 'type' => 'structure', 'required' => [ 'principalArn', ], 'members' => [ 'cloudTrailProperties' => [ 'shape' => 'CloudTrailProperties', ], 'isComplete' => [ 'shape' => 'Boolean', ], 'principalArn' => [ 'shape' => 'PrincipalArn', ], ], ], 'GeneratedPolicyResult' => [ 'type' => 'structure', 'required' => [ 'properties', ], 'members' => [ 'generatedPolicies' => [ 'shape' => 'GeneratedPolicyList', ], 'properties' => [ 'shape' => 'GeneratedPolicyProperties', ], ], ], 'GetAccessPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'accessPreviewId', 'analyzerArn', ], 'members' => [ 'accessPreviewId' => [ 'shape' => 'AccessPreviewId', 'location' => 'uri', 'locationName' => 'accessPreviewId', ], 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], ], ], 'GetAccessPreviewResponse' => [ 'type' => 'structure', 'required' => [ 'accessPreview', ], 'members' => [ 'accessPreview' => [ 'shape' => 'AccessPreview', ], ], ], 'GetAnalyzedResourceRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'resourceArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'GetAnalyzedResourceResponse' => [ 'type' => 'structure', 'members' => [ 'resource' => [ 'shape' => 'AnalyzedResource', ], ], ], 'GetAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], ], ], 'GetAnalyzerResponse' => [ 'type' => 'structure', 'required' => [ 'analyzer', ], 'members' => [ 'analyzer' => [ 'shape' => 'AnalyzerSummary', ], ], ], 'GetArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'ruleName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'ruleName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'GetArchiveRuleResponse' => [ 'type' => 'structure', 'required' => [ 'archiveRule', ], 'members' => [ 'archiveRule' => [ 'shape' => 'ArchiveRuleSummary', ], ], ], 'GetFindingRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'id', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'id' => [ 'shape' => 'FindingId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetFindingResponse' => [ 'type' => 'structure', 'members' => [ 'finding' => [ 'shape' => 'Finding', ], ], ], 'GetGeneratedPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'includeResourcePlaceholders' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeResourcePlaceholders', ], 'includeServiceLevelTemplate' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeServiceLevelTemplate', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetGeneratedPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'generatedPolicyResult', 'jobDetails', ], 'members' => [ 'generatedPolicyResult' => [ 'shape' => 'GeneratedPolicyResult', ], 'jobDetails' => [ 'shape' => 'JobDetails', ], ], ], 'GranteePrincipal' => [ 'type' => 'string', ], 'IamRoleConfiguration' => [ 'type' => 'structure', 'members' => [ 'trustPolicy' => [ 'shape' => 'IamTrustPolicy', ], ], ], 'IamTrustPolicy' => [ 'type' => 'string', ], 'InlineArchiveRule' => [ 'type' => 'structure', 'required' => [ 'filter', 'ruleName', ], 'members' => [ 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'ruleName' => [ 'shape' => 'Name', ], ], ], 'InlineArchiveRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InlineArchiveRule', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InternetConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'IssueCode' => [ 'type' => 'string', ], 'IssuingAccount' => [ 'type' => 'string', ], 'JobDetails' => [ 'type' => 'structure', 'required' => [ 'jobId', 'startedOn', 'status', ], 'members' => [ 'completedOn' => [ 'shape' => 'Timestamp', ], 'jobError' => [ 'shape' => 'JobError', ], 'jobId' => [ 'shape' => 'JobId', ], 'startedOn' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'JobError' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'JobErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'JobErrorCode' => [ 'type' => 'string', 'enum' => [ 'AUTHORIZATION_ERROR', 'RESOURCE_NOT_FOUND_ERROR', 'SERVICE_QUOTA_EXCEEDED_ERROR', 'SERVICE_ERROR', ], ], 'JobId' => [ 'type' => 'string', ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'CANCELED', ], ], 'KmsConstraintsKey' => [ 'type' => 'string', ], 'KmsConstraintsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KmsConstraintsKey', ], 'value' => [ 'shape' => 'KmsConstraintsValue', ], ], 'KmsConstraintsValue' => [ 'type' => 'string', ], 'KmsGrantConfiguration' => [ 'type' => 'structure', 'required' => [ 'granteePrincipal', 'issuingAccount', 'operations', ], 'members' => [ 'constraints' => [ 'shape' => 'KmsGrantConstraints', ], 'granteePrincipal' => [ 'shape' => 'GranteePrincipal', ], 'issuingAccount' => [ 'shape' => 'IssuingAccount', ], 'operations' => [ 'shape' => 'KmsGrantOperationsList', ], 'retiringPrincipal' => [ 'shape' => 'RetiringPrincipal', ], ], ], 'KmsGrantConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KmsGrantConfiguration', ], ], 'KmsGrantConstraints' => [ 'type' => 'structure', 'members' => [ 'encryptionContextEquals' => [ 'shape' => 'KmsConstraintsMap', ], 'encryptionContextSubset' => [ 'shape' => 'KmsConstraintsMap', ], ], ], 'KmsGrantOperation' => [ 'type' => 'string', 'enum' => [ 'CreateGrant', 'Decrypt', 'DescribeKey', 'Encrypt', 'GenerateDataKey', 'GenerateDataKeyPair', 'GenerateDataKeyPairWithoutPlaintext', 'GenerateDataKeyWithoutPlaintext', 'GetPublicKey', 'ReEncryptFrom', 'ReEncryptTo', 'RetireGrant', 'Sign', 'Verify', ], ], 'KmsGrantOperationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KmsGrantOperation', ], ], 'KmsKeyConfiguration' => [ 'type' => 'structure', 'members' => [ 'grants' => [ 'shape' => 'KmsGrantConfigurationsList', ], 'keyPolicies' => [ 'shape' => 'KmsKeyPoliciesMap', ], ], ], 'KmsKeyPoliciesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PolicyName', ], 'value' => [ 'shape' => 'KmsKeyPolicy', ], ], 'KmsKeyPolicy' => [ 'type' => 'string', ], 'LearnMoreLink' => [ 'type' => 'string', ], 'ListAccessPreviewFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'accessPreviewId', 'analyzerArn', ], 'members' => [ 'accessPreviewId' => [ 'shape' => 'AccessPreviewId', 'location' => 'uri', 'locationName' => 'accessPreviewId', ], 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccessPreviewFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'AccessPreviewFindingsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccessPreviewsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAccessPreviewsResponse' => [ 'type' => 'structure', 'required' => [ 'accessPreviews', ], 'members' => [ 'accessPreviews' => [ 'shape' => 'AccessPreviewsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAnalyzedResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'Token', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], ], 'ListAnalyzedResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'analyzedResources', ], 'members' => [ 'analyzedResources' => [ 'shape' => 'AnalyzedResourcesList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAnalyzersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'type' => [ 'shape' => 'Type', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'ListAnalyzersResponse' => [ 'type' => 'structure', 'required' => [ 'analyzers', ], 'members' => [ 'analyzers' => [ 'shape' => 'AnalyzersList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListArchiveRulesRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListArchiveRulesResponse' => [ 'type' => 'structure', 'required' => [ 'archiveRules', ], 'members' => [ 'archiveRules' => [ 'shape' => 'ArchiveRulesList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'Token', ], 'sort' => [ 'shape' => 'SortCriteria', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'FindingsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListPolicyGenerationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListPolicyGenerationsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'principalArn' => [ 'shape' => 'PrincipalArn', 'location' => 'querystring', 'locationName' => 'principalArn', ], ], ], 'ListPolicyGenerationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ListPolicyGenerationsResponse' => [ 'type' => 'structure', 'required' => [ 'policyGenerations', ], 'members' => [ 'nextToken' => [ 'shape' => 'Token', ], 'policyGenerations' => [ 'shape' => 'PolicyGenerationList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'Locale' => [ 'type' => 'string', 'enum' => [ 'DE', 'EN', 'ES', 'FR', 'IT', 'JA', 'KO', 'PT_BR', 'ZH_CN', 'ZH_TW', ], ], 'Location' => [ 'type' => 'structure', 'required' => [ 'path', 'span', ], 'members' => [ 'path' => [ 'shape' => 'PathElementList', ], 'span' => [ 'shape' => 'Span', ], ], ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Location', ], ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z][A-Za-z0-9_.-]*', ], 'NetworkOriginConfiguration' => [ 'type' => 'structure', 'members' => [ 'internetConfiguration' => [ 'shape' => 'InternetConfiguration', ], 'vpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], ], 'union' => true, ], 'OrderBy' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'PathElement' => [ 'type' => 'structure', 'members' => [ 'index' => [ 'shape' => 'Integer', ], 'key' => [ 'shape' => 'String', ], 'substring' => [ 'shape' => 'Substring', ], 'value' => [ 'shape' => 'String', ], ], 'union' => true, ], 'PathElementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathElement', ], ], 'PolicyDocument' => [ 'type' => 'string', ], 'PolicyGeneration' => [ 'type' => 'structure', 'required' => [ 'jobId', 'principalArn', 'startedOn', 'status', ], 'members' => [ 'completedOn' => [ 'shape' => 'Timestamp', ], 'jobId' => [ 'shape' => 'JobId', ], 'principalArn' => [ 'shape' => 'PrincipalArn', ], 'startedOn' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'PolicyGenerationDetails' => [ 'type' => 'structure', 'required' => [ 'principalArn', ], 'members' => [ 'principalArn' => [ 'shape' => 'PrincipalArn', ], ], ], 'PolicyGenerationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyGeneration', ], ], 'PolicyName' => [ 'type' => 'string', ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'IDENTITY_POLICY', 'RESOURCE_POLICY', 'SERVICE_CONTROL_POLICY', ], ], 'Position' => [ 'type' => 'structure', 'required' => [ 'column', 'line', 'offset', ], 'members' => [ 'column' => [ 'shape' => 'Integer', ], 'line' => [ 'shape' => 'Integer', ], 'offset' => [ 'shape' => 'Integer', ], ], ], 'PrincipalArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iam::[^:]*:(role|user)/.{1,576}', ], 'PrincipalMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ReasonCode' => [ 'type' => 'string', 'enum' => [ 'AWS_SERVICE_ACCESS_DISABLED', 'DELEGATED_ADMINISTRATOR_DEREGISTERED', 'ORGANIZATION_DELETED', 'SERVICE_LINKED_ROLE_CREATION_FAILED', ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:.*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::S3::Bucket', 'AWS::IAM::Role', 'AWS::SQS::Queue', 'AWS::Lambda::Function', 'AWS::Lambda::LayerVersion', 'AWS::KMS::Key', 'AWS::SecretsManager::Secret', ], ], 'RetiringPrincipal' => [ 'type' => 'string', ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iam::[^:]*:role/.{1,576}', ], 'S3AccessPointConfiguration' => [ 'type' => 'structure', 'members' => [ 'accessPointPolicy' => [ 'shape' => 'AccessPointPolicy', ], 'networkOrigin' => [ 'shape' => 'NetworkOriginConfiguration', ], 'publicAccessBlock' => [ 'shape' => 'S3PublicAccessBlockConfiguration', ], ], ], 'S3AccessPointConfigurationsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AccessPointArn', ], 'value' => [ 'shape' => 'S3AccessPointConfiguration', ], ], 'S3BucketAclGrantConfiguration' => [ 'type' => 'structure', 'required' => [ 'grantee', 'permission', ], 'members' => [ 'grantee' => [ 'shape' => 'AclGrantee', ], 'permission' => [ 'shape' => 'AclPermission', ], ], ], 'S3BucketAclGrantConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketAclGrantConfiguration', ], ], 'S3BucketConfiguration' => [ 'type' => 'structure', 'members' => [ 'accessPoints' => [ 'shape' => 'S3AccessPointConfigurationsMap', ], 'bucketAclGrants' => [ 'shape' => 'S3BucketAclGrantConfigurationsList', ], 'bucketPolicy' => [ 'shape' => 'S3BucketPolicy', ], 'bucketPublicAccessBlock' => [ 'shape' => 'S3PublicAccessBlockConfiguration', ], ], ], 'S3BucketPolicy' => [ 'type' => 'string', ], 'S3PublicAccessBlockConfiguration' => [ 'type' => 'structure', 'required' => [ 'ignorePublicAcls', 'restrictPublicBuckets', ], 'members' => [ 'ignorePublicAcls' => [ 'shape' => 'Boolean', ], 'restrictPublicBuckets' => [ 'shape' => 'Boolean', ], ], ], 'SecretsManagerSecretConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'SecretsManagerSecretKmsId', ], 'secretPolicy' => [ 'shape' => 'SecretsManagerSecretPolicy', ], ], ], 'SecretsManagerSecretKmsId' => [ 'type' => 'string', ], 'SecretsManagerSecretPolicy' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SharedViaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => 'String', ], 'orderBy' => [ 'shape' => 'OrderBy', ], ], ], 'Span' => [ 'type' => 'structure', 'required' => [ 'end', 'start', ], 'members' => [ 'end' => [ 'shape' => 'Position', ], 'start' => [ 'shape' => 'Position', ], ], ], 'SqsQueueConfiguration' => [ 'type' => 'structure', 'members' => [ 'queuePolicy' => [ 'shape' => 'SqsQueuePolicy', ], ], ], 'SqsQueuePolicy' => [ 'type' => 'string', ], 'StartPolicyGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'policyGenerationDetails', ], 'members' => [ 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'cloudTrailDetails' => [ 'shape' => 'CloudTrailDetails', ], 'policyGenerationDetails' => [ 'shape' => 'PolicyGenerationDetails', ], ], ], 'StartPolicyGenerationResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'StartResourceScanRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'resourceArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'StatusReason' => [ 'type' => 'structure', 'required' => [ 'code', ], 'members' => [ 'code' => [ 'shape' => 'ReasonCode', ], ], ], 'String' => [ 'type' => 'string', ], 'Substring' => [ 'type' => 'structure', 'required' => [ 'length', 'start', ], 'members' => [ 'length' => [ 'shape' => 'Integer', ], 'start' => [ 'shape' => 'Integer', ], ], ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Token' => [ 'type' => 'string', ], 'Trail' => [ 'type' => 'structure', 'required' => [ 'cloudTrailArn', ], 'members' => [ 'allRegions' => [ 'shape' => 'Boolean', ], 'cloudTrailArn' => [ 'shape' => 'CloudTrailArn', ], 'regions' => [ 'shape' => 'RegionList', ], ], ], 'TrailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trail', ], ], 'TrailProperties' => [ 'type' => 'structure', 'required' => [ 'cloudTrailArn', ], 'members' => [ 'allRegions' => [ 'shape' => 'Boolean', ], 'cloudTrailArn' => [ 'shape' => 'CloudTrailArn', ], 'regions' => [ 'shape' => 'RegionList', ], ], ], 'TrailPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrailProperties', ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATION', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'filter', 'ruleName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'ruleName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'UpdateFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'status', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'ids' => [ 'shape' => 'FindingIdList', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'status' => [ 'shape' => 'FindingStatusUpdate', ], ], ], 'ValidatePolicyFinding' => [ 'type' => 'structure', 'required' => [ 'findingDetails', 'findingType', 'issueCode', 'learnMoreLink', 'locations', ], 'members' => [ 'findingDetails' => [ 'shape' => 'String', ], 'findingType' => [ 'shape' => 'ValidatePolicyFindingType', ], 'issueCode' => [ 'shape' => 'IssueCode', ], 'learnMoreLink' => [ 'shape' => 'LearnMoreLink', ], 'locations' => [ 'shape' => 'LocationList', ], ], ], 'ValidatePolicyFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidatePolicyFinding', ], ], 'ValidatePolicyFindingType' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'SECURITY_WARNING', 'SUGGESTION', 'WARNING', ], ], 'ValidatePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyDocument', 'policyType', ], 'members' => [ 'locale' => [ 'shape' => 'Locale', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'validatePolicyResourceType' => [ 'shape' => 'ValidatePolicyResourceType', ], ], ], 'ValidatePolicyResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::S3::Bucket', 'AWS::S3::AccessPoint', 'AWS::S3::MultiRegionAccessPoint', 'AWS::S3ObjectLambda::AccessPoint', ], ], 'ValidatePolicyResponse' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'ValidatePolicyFindingList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'ValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 20, 'min' => 1, ], 'VpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'vpcId', ], 'members' => [ 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'VpcId' => [ 'type' => 'string', 'pattern' => 'vpc-([0-9a-f]){8}(([0-9a-f]){9})?', ], ],];
