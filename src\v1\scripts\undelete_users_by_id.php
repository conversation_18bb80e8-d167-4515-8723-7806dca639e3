#!/usr/bin/php
<?php
require_once "../models/log_helper.php";   // Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php";   // Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php";     // Implementa la classe di connessione a postgres
require_once "../models/class_pdb.php";    // Implementa la classe di connessione a postgres
require_once "../models/class_couch.php";  // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php";   // Implementa la classe Data generica di accesso ai dati mastercom

if (count($argv) < 2) {
    echo "undelete_users_by_id.php <id1,id2,id3,...> [db]\n";
    echo "Ripristina gli utenti specificati impostando access_level = 'user'\n";
    echo "db: default mastercom. Specifica il database CouchDB da utilizzare\n";
    exit;
}

$ids = explode(',', $argv[1]);
$db = isset($argv[2]) ? $argv[2] : 'mastercom';

if (!(in_array($db, ['mastercom', 'calendar', 'archive', 'documents']))) {
    $db = 'mastercom';
}

$data = new Data;
if (true) {
    $previous = $data->SetCdb($db);
    
    if ($previous !== false) {
        foreach ($ids as $id) {
            echo "Elaborazione $id: ";
            
            // Ottieni il documento
            $doc = $data->cdb->call($id);
            if (!isset($doc['_id'])) {
                echo "documento non trovato\n";
                continue;
            }

            // Verifica se il documento ha il campo access_level
            if (!isset($doc['access_level'])) {
                echo "campo access_level non presente\n";
                continue;
            }

            // Imposta access_level a 'user'
            $doc['access_level'] = 'user';
            
            // Salva il documento
            $result = $data->cdb->call($id, 'PUT', $doc);
            if ($result['ok']) {
                echo "ripristinato con access_level = 'user'\n";
            } else {
                echo "errore durante il ripristino: ";
                print_r($result);
                echo "\n";
            }
        }
    } else {
        echo "Errore nella connessione al database\n";
    }
} else {
    echo "scripts blocked\n";
}