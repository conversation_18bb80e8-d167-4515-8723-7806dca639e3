<?php
require_once "../models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php"; 	// Implementa la classe di connessione a postgres 
require_once "../models/class_pdb.php"; 	// Implementa la classe di connessione a postgres 
require_once "../models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom
require_once "../models/class_auth.php";  // Implementa la classe Auth per l'autenticazione
require_once "../models/class_marketplace.php"; // Implementa la classe Marketplace

// acquisisci utente e password dal terminale
$username = $argv[1];
$password = $argv[2];
$anno_scolastico = $argv[3]; // anno scolastico obbligatorio

if (empty($username) || empty($password) || empty($anno_scolastico)) {
    die("Usage: php test_verifica_tipi_movimento_marketplace.php <username> <password> <anno_scolastico>\n" .
        "Esempio: php test_verifica_tipi_movimento_marketplace.php admin password 2023/2024\n");
}

// Inizializza gli oggetti necessari
$data = new Data();
$auth = new Auth($data);

// Effettua il login
$authorized = $auth->auth($username, $password);

if ($authorized) {
    echo "Login successful.\n";
    
    // Istanzia Marketplace
    $marketplace = new Marketplace($auth);
    
    // Parametri per la richiesta
    $input = [
        'anno_scolastico' => $anno_scolastico
    ];

    echo "Eseguendo verifica tipi movimento marketplace per anno scolastico: {$anno_scolastico}...\n";
    
    // Esegui la verifica
    $risultato = $marketplace->verificaTipiMovimentoMarketplace($input);
    
    // Stampa il risultato
    echo "\nRisultato verifica:\n";
    echo json_encode($risultato, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "\n";
    
    // Stampa un riepilogo
    if ($risultato['esito'] === 'OK') {
        echo "\n=== RIEPILOGO ===\n";
        echo "Anno scolastico verificato: " . $risultato['anno_scolastico_verificato'] . "\n";
        echo "Database utilizzato: " . $risultato['database_utilizzato'] . "\n";
        echo "Totale record verificati: " . $risultato['totale_records_verificati'] . "\n";
        echo "Record validi: " . $risultato['conteggi']['validi'] . "\n";
        echo "Record non validi: " . $risultato['conteggi']['non_validi'] . "\n";
        echo "Errori chiamate MC2: " . $risultato['conteggi']['errori'] . "\n";
        
        if ($risultato['conteggi']['validi'] > 0) {
            echo "\n=== RECORD VALIDI ===\n";
            foreach ($risultato['records_validi'] as $record) {
                echo "- ID Marketplace: " . $record['id_marketplace'] .
                     " | Descrizione: " . $record['descrizione'] .
                     " | ID Tipo Movimento: " . $record['id_tipo_movimento'] .
                     " | Anno Tipo: " . $record['school_year_tipo'] .
                     " | Confronto: " . $record['confronto'] . "\n";
            }
        }

        if ($risultato['conteggi']['non_validi'] > 0) {
            echo "\n=== RECORD NON VALIDI ===\n";
            foreach ($risultato['records_non_validi'] as $record) {
                echo "- ID Marketplace: " . $record['id_marketplace'] .
                     " | Descrizione: " . $record['descrizione'] .
                     " | ID Tipo Movimento: " . $record['id_tipo_movimento'] .
                     " | Anno Tipo: " . $record['school_year_tipo'] .
                     " | Anno Verificato: " . $record['anno_scolastico_verificato'] . "\n";
            }
        }
        
        if ($risultato['conteggi']['errori'] > 0) {
            echo "\n=== ERRORI CHIAMATE MC2 ===\n";
            foreach ($risultato['errori_chiamate_mc2'] as $errore) {
                echo "- ID Marketplace: " . $errore['id_marketplace'] . 
                     " | Descrizione: " . $errore['descrizione'] . 
                     " | ID Tipo Movimento: " . $errore['id_tipo_movimento'] . 
                     " | Motivo: " . $errore['motivo'] . "\n";
            }
        }
    }
    
} else {
    echo "Login failed: Invalid username or password\n";
    exit(1);
}
?>
