<?php
/********************
 * Roba disponibile:
 * $auth -> oggetto di autenticazione con dati utente autenticato e accesso db
 * $method -> GET/PUT/POST/DELETE o altre richieste http valide
 * $request -> array path richiesta (elemento 0 ha portato qua)
 * $query -> array variabili incluse nella richiesta
 *
 ********************/

require_once "models/class_marketplace.php"; 	
require_once "models/class_assenze.php"; 	
$marketplace = new Marketplace($auth);

if(count($body) > 0){
	$input = $body;
}else{
	$input = $input;
}

switch ($method) {
case 'GET':
	if(is_numeric($request[1]))	{
		$filter['id'] = $request[1];
		if(strlen($input['db_richiesto']))	{
			$filter['db_richiesto'] = $input['db_richiesto'];
		}
		$dati = $marketplace->getListaMarketplace($filter);
		header('Content-type: application/json; charset=utf-8');
		echo json_encode($dati);
	} else {
		switch ($request[1]) {
			case null:
				$dati = $marketplace->getListaMarketplace($input);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;		
			case 'acquisti':
				$data = (count($query) > 0) ? $query : $input;

				if(is_numeric($request[2]))	{
					$filter['id'] = $request[2];
					if(strlen($data['db_richiesto']))	{
						$filter['db_richiesto'] = $data['db_richiesto'];
					}
					$dati = $marketplace->getListaMarketplaceAcquisti($filter);
					header('Content-type: application/json; charset=utf-8');
					echo json_encode($dati);
				}else{			
					switch ($request[2]) {
						case null:
						default:					
							$dati = $marketplace->getListaMarketplaceAcquisti($data);
							header('Content-type: application/json; charset=utf-8');
							echo json_encode($dati);
							break;
						}      
					} 
				break;
			case 'lista_prenotazioni_giornaliere':
				$data = (count($query) > 0) ? $query : $input;

				$filter = $data;
				if(is_numeric($request[2]))	{
					$filter['id_studente'] = $request[2];
					if(strlen($data['db_richiesto']))	{
						$filter['db_richiesto'] = $data['db_richiesto'];
					}
					if(is_numeric($request[3]))	{
						$filter['id_marketplace'] = $request[3];
					}
				}

				$dati = $marketplace->getListaPrenotazioniGiornaliere($filter);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			case 'credito_studente_servizi_giornalieri':
				$data = (count($query) > 0) ? $query : $input;

				$filter = $data;
				if(is_numeric($request[2]))	{
					$filter['id_studente'] = $request[2];
					if(strlen($data['db_richiesto']))	{
						$filter['db_richiesto'] = $data['db_richiesto'];
					}
					if(is_numeric($request[3]))	{
						$filter['id_marketplace'] = $request[3];
					}
				}

				$dati = $marketplace->getCreditoPrenotazioniGiornaliere($filter);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;				
			case 'extratime':
					$dati = $marketplace->getConsolidamentoServizio($input);
					header('Content-type: application/json; charset=utf-8');
					echo json_encode($dati);
				break;		
			case 'lista_oggetti_negozio_studente':
				$dati = $marketplace->getConsolidamentoServizio($input);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			case 'lista_marketplace_senza_tipo_movimento':
				$dati = $marketplace->getMarketplaceRecordsWithPreviousYear($input);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			case 'verifica_tipi_movimento':
				$dati = $marketplace->verificaTipiMovimentoMarketplace($input);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			default:
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($input);
				break;
		}
	}
	break;
case 'PUT':
	switch ($request[1]) {
		case 'acquisti':
				switch ($request[2]) {
					case 'modifica':				
						$dati = $marketplace->modificaMarketplaceAcquisto($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;		
					case null:
					default:
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($input);
						break;
					}      
			break;
		case 'servizi_giornalieri':
			$data = (count($query) > 0) ? $query : $input;
			switch ($request[2]) {
				case 'inserisci_assenza':				
					$dati = $marketplace->inserisciAssenzaGiornalieraServizio($data);
					header('Content-type: application/json; charset=utf-8');
					echo json_encode($dati);
					break;		
				case 'aggiorna_adesioni_studente':				
					$dati = $marketplace->aggiornaAdesioniPeriodoServizioGiornalieroStudente($data);
					header('Content-type: application/json; charset=utf-8');
					echo json_encode($dati);
					break;		
				case null:
				default:
					header('Content-type: application/json; charset=utf-8');
					echo json_encode($input);
					break;
				}      
		break;		
		case null:
		default:
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($input);
			break;
		}	
	break;
case 'POST':
		switch ($request[1]) {
			case 'inserisci':
				$dati = $marketplace->inserisciMarketplaceItem($input);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			case 'modifica':
				if ($request[2] > 0){
					$id_marketplace = $request[2];
				} elseif ($input['id_marketplace'] > 0){
					$id_marketplace = $input['id_marketplace'];
					unset($input['id_marketplace']);
				}

				if ($id_marketplace > 0){ 
					$dati = $marketplace->modificaMarketplaceItem($id_marketplace, $input);
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			case 'acquisti':
					switch ($request[2]) {
						case 'inserisci':
							$dati = $marketplace->inserisciMarketplaceAcquisto($input);
							header('Content-type: application/json; charset=utf-8');
							echo json_encode($dati);
							break;		
						case null:
						default:
							header('Content-type: application/json; charset=utf-8');
							echo json_encode($input);
							break;
						}      
				break;
			case 'adesioni_giornaliere':
				switch ($request[2]) {
					case 'inserisci':
						$dati = $marketplace->inserisciAdesioneGiornalieraServizioStudente($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;		
					case 'presenza':
						$dati = $marketplace->inserisciPresenzaGiornalieraServizioStudente($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;		
					case null:
					default:
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($input);
						break;
					}      
				break;
			case 'consolidamenti':
				switch ($request[2]) {
					case 'inserisci':
						$dati = $marketplace->inserisciConsolidamento($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;		
					case null:
					default:
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($input);
						break;
					}      
				break;				
			case null:
			default:
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($input);
				break;
			}	
	break;
case 'DELETE':
		switch ($request[1]) {
			case 'elimina':
				$dati = $marketplace->softDeleteItemMarketplace($input);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($dati);
				break;
			case 'acquisti':
				switch ($request[2]) {
					case 'elimina':
						$dati = $marketplace->softDeleteItemAcquisti($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;		
					case null:
					default:
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($input);
						break;
					}      
				break;
			case 'adesioni_giornaliere':
				switch ($request[2]) {
					case 'elimina':
						$dati = $marketplace->softDeleteAdesioneGiornalieraServizioStudente($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;
					case 'presenza':
						$dati = $marketplace->eliminaPresenzaGiornalieraServizioStudente($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;		
					case 'consolidamento':
						$dati = $marketplace->softDeleteAdesioneGiornalieraConsolidamento($input);
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($dati);
						break;
					case null:
					default:
						header('Content-type: application/json; charset=utf-8');
						echo json_encode($input);
						break;
					}      
				break;
			case null:
			default:
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($input);
				break;
			}	
	break;
}

?>
