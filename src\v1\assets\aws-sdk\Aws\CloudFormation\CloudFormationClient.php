<?php
namespace Aws\CloudFormation;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS CloudFormation** service.
 *
 * @method \Aws\Result activateType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise activateTypeAsync(array $args = [])
 * @method \Aws\Result batchDescribeTypeConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDescribeTypeConfigurationsAsync(array $args = [])
 * @method \Aws\Result cancelUpdateStack(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelUpdateStackAsync(array $args = [])
 * @method \Aws\Result continueUpdateRollback(array $args = [])
 * @method \GuzzleHttp\Promise\Promise continueUpdateRollbackAsync(array $args = [])
 * @method \Aws\Result createChangeSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChangeSetAsync(array $args = [])
 * @method \Aws\Result createStack(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStackAsync(array $args = [])
 * @method \Aws\Result createStackInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStackInstancesAsync(array $args = [])
 * @method \Aws\Result createStackSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStackSetAsync(array $args = [])
 * @method \Aws\Result deactivateType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deactivateTypeAsync(array $args = [])
 * @method \Aws\Result deleteChangeSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChangeSetAsync(array $args = [])
 * @method \Aws\Result deleteStack(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStackAsync(array $args = [])
 * @method \Aws\Result deleteStackInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStackInstancesAsync(array $args = [])
 * @method \Aws\Result deleteStackSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStackSetAsync(array $args = [])
 * @method \Aws\Result deregisterType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deregisterTypeAsync(array $args = [])
 * @method \Aws\Result describeAccountLimits(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAccountLimitsAsync(array $args = [])
 * @method \Aws\Result describeChangeSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeChangeSetAsync(array $args = [])
 * @method \Aws\Result describeChangeSetHooks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeChangeSetHooksAsync(array $args = [])
 * @method \Aws\Result describePublisher(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePublisherAsync(array $args = [])
 * @method \Aws\Result describeStackDriftDetectionStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackDriftDetectionStatusAsync(array $args = [])
 * @method \Aws\Result describeStackEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackEventsAsync(array $args = [])
 * @method \Aws\Result describeStackInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackInstanceAsync(array $args = [])
 * @method \Aws\Result describeStackResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackResourceAsync(array $args = [])
 * @method \Aws\Result describeStackResourceDrifts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackResourceDriftsAsync(array $args = [])
 * @method \Aws\Result describeStackResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackResourcesAsync(array $args = [])
 * @method \Aws\Result describeStackSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackSetAsync(array $args = [])
 * @method \Aws\Result describeStackSetOperation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStackSetOperationAsync(array $args = [])
 * @method \Aws\Result describeStacks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStacksAsync(array $args = [])
 * @method \Aws\Result describeType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTypeAsync(array $args = [])
 * @method \Aws\Result describeTypeRegistration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTypeRegistrationAsync(array $args = [])
 * @method \Aws\Result detectStackDrift(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detectStackDriftAsync(array $args = [])
 * @method \Aws\Result detectStackResourceDrift(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detectStackResourceDriftAsync(array $args = [])
 * @method \Aws\Result detectStackSetDrift(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detectStackSetDriftAsync(array $args = [])
 * @method \Aws\Result estimateTemplateCost(array $args = [])
 * @method \GuzzleHttp\Promise\Promise estimateTemplateCostAsync(array $args = [])
 * @method \Aws\Result executeChangeSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeChangeSetAsync(array $args = [])
 * @method \Aws\Result getStackPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStackPolicyAsync(array $args = [])
 * @method \Aws\Result getTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateAsync(array $args = [])
 * @method \Aws\Result getTemplateSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateSummaryAsync(array $args = [])
 * @method \Aws\Result importStacksToStackSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importStacksToStackSetAsync(array $args = [])
 * @method \Aws\Result listChangeSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChangeSetsAsync(array $args = [])
 * @method \Aws\Result listExports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExportsAsync(array $args = [])
 * @method \Aws\Result listImports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listImportsAsync(array $args = [])
 * @method \Aws\Result listStackInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStackInstancesAsync(array $args = [])
 * @method \Aws\Result listStackResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStackResourcesAsync(array $args = [])
 * @method \Aws\Result listStackSetOperationResults(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStackSetOperationResultsAsync(array $args = [])
 * @method \Aws\Result listStackSetOperations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStackSetOperationsAsync(array $args = [])
 * @method \Aws\Result listStackSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStackSetsAsync(array $args = [])
 * @method \Aws\Result listStacks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStacksAsync(array $args = [])
 * @method \Aws\Result listTypeRegistrations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTypeRegistrationsAsync(array $args = [])
 * @method \Aws\Result listTypeVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTypeVersionsAsync(array $args = [])
 * @method \Aws\Result listTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTypesAsync(array $args = [])
 * @method \Aws\Result publishType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise publishTypeAsync(array $args = [])
 * @method \Aws\Result recordHandlerProgress(array $args = [])
 * @method \GuzzleHttp\Promise\Promise recordHandlerProgressAsync(array $args = [])
 * @method \Aws\Result registerPublisher(array $args = [])
 * @method \GuzzleHttp\Promise\Promise registerPublisherAsync(array $args = [])
 * @method \Aws\Result registerType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise registerTypeAsync(array $args = [])
 * @method \Aws\Result rollbackStack(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rollbackStackAsync(array $args = [])
 * @method \Aws\Result setStackPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise setStackPolicyAsync(array $args = [])
 * @method \Aws\Result setTypeConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise setTypeConfigurationAsync(array $args = [])
 * @method \Aws\Result setTypeDefaultVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise setTypeDefaultVersionAsync(array $args = [])
 * @method \Aws\Result signalResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise signalResourceAsync(array $args = [])
 * @method \Aws\Result stopStackSetOperation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopStackSetOperationAsync(array $args = [])
 * @method \Aws\Result testType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise testTypeAsync(array $args = [])
 * @method \Aws\Result updateStack(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStackAsync(array $args = [])
 * @method \Aws\Result updateStackInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStackInstancesAsync(array $args = [])
 * @method \Aws\Result updateStackSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStackSetAsync(array $args = [])
 * @method \Aws\Result updateTerminationProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTerminationProtectionAsync(array $args = [])
 * @method \Aws\Result validateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise validateTemplateAsync(array $args = [])
 */
class CloudFormationClient extends AwsClient {}
