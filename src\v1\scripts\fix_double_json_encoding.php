#!/usr/bin/php
<?php
require_once "../models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom
require_once "../models/class_auth.php";  // Implementa la classe Auth per l'autenticazione
require_once "../models/class_masterAI.php"; // Implementa la classe MasterAI

// Verifica argomenti da linea di comando
if ($argc < 3) {
    echo "Uso: php fix_double_json_encoding.php <username> <password>\n";
    exit(1);
}

$username = $argv[1];
$password = $argv[2];

// Inizializzazione e autenticazione
$data = new Data;
$auth = new Auth($data);

// Tentativo di autenticazione
if (!$auth->auth($username, $password)) {
    echo "Errore di autenticazione: credenziali non valide\n";
    exit(1);
}

// Verifica che l'utente sia un amministratore
if (!$auth->isAdmin()) {
    echo "Errore: l'utente non ha i privilegi di amministratore necessari\n";
    exit(1);
}

function getError() {
    switch (json_last_error()) {
        case JSON_ERROR_NONE:
            echo ' - No errors';
        break;
        case JSON_ERROR_DEPTH:
            echo ' - Maximum stack depth exceeded';
        break;
        case JSON_ERROR_STATE_MISMATCH:
            echo ' - Underflow or the modes mismatch';
        break;
        case JSON_ERROR_CTRL_CHAR:
            echo ' - Unexpected control character found';
        break;
        case JSON_ERROR_SYNTAX:
            echo ' - Syntax error, malformed JSON';
        break;
        case JSON_ERROR_UTF8:
            echo ' - Malformed UTF-8 characters, possibly incorrectly encoded';
        break;
        default:
            echo ' - Unknown error';
        break;
    }
}

function fixJsonField($value) {
    if (empty($value)) {
        return $value;
    }
    
    // First decode attempt
    $decoded = json_decode($value, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        // If it's already a string representation of JSON, decode it again
        if (is_string($decoded)) {
            $decoded = json_decode($decoded, true);
        }
        // Re-encode properly
        return json_encode($decoded);
    }
    
    return $value;
}

// Log dell'inizio operazione
Logging::debug("Inizio operazione fix_double_json_encoding da utente: " . $username);

if (true) {
    //$previous = $data->SetDb('mastercom');
    
    // Get all records from ai_results
    $sql = "SELECT * FROM ai_results";
    $query = $data->db->prepare($sql);
    $query->execute();
    $results = $query->fetchAll(PDO::FETCH_ASSOC);

    $updated = 0;
    $errors = 0;
    
    foreach ($results as $record) {
        $needsUpdate = false;
        $updateData = [];

        // Check and fix each JSON field
        $fields = ['parametri', 'result_data', 'result_row_data'];
        foreach ($fields as $field) {
            if (!empty($record[$field])) {
                $fixed = fixJsonField($record[$field]);
                if ($fixed !== $record[$field]) {
                    $needsUpdate = true;
                    $updateData[$field] = $fixed;
                }
            }
        }
        
        // Update record if needed
        if ($needsUpdate) {
            try {
                $setClauses = [];
                $params = [];
                foreach ($updateData as $field => $value) {
                    $setClauses[] = "$field = :$field";
                    $params[$field] = $value;
                }
                
                $sql = "UPDATE ai_results SET " . implode(', ', $setClauses) . " WHERE id = :id";
                $params['id'] = $record['id'];
                
                $query = $data->db->prepare($sql);
                $result = $query->execute($params);
                
                if ($result) {
                    $updated++;
                    echo "Updated record ID: " . $record['id'] . "\n";
                    Logging::debug("Aggiornato record ID: " . $record['id']);
                } else {
                    $errors++;
                    echo "Failed to update record ID: " . $record['id'] . "\n";
                    Logging::debug("Fallito aggiornamento record ID: " . $record['id']);
                }
            } catch (Exception $e) {
                $errors++;
                $errorMessage = "Error updating record ID: " . $record['id'] . " - " . $e->getMessage();
                echo $errorMessage . "\n";
                Logging::debug($errorMessage);
            }
        }
    }
    
    $summary = "\nProcess completed.\nTotal records updated: $updated\nTotal errors: $errors\n";
    echo $summary;
    Logging::debug("Operazione completata - Aggiornati: $updated - Errori: $errors");
    
    //$data->SetDb($previous);
}
