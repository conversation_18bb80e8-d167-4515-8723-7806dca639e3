# API Verifica Tipi Movimento Marketplace

## Descrizione
Questa API verifica che i record di marketplace abbiano un `id_tipo_movimento` che corrisponde ad un tipo movimento che ha come `school_year` uguale o maggiore all'anno scolastico specificato. L'API si collega al database dell'anno scolastico specificato per effettuare i controlli.

## Endpoint
```
GET /v1/marketplace/verifica_tipi_movimento
```

## Parametri
- `anno_scolastico` (obbligatorio): Anno scolastico da verificare nel formato YYYY/YYYY (es. "2023/2024")

## Esempio di chiamata

### Via cURL
```bash
curl -X GET "http://localhost/next-api/src/v1/marketplace/verifica_tipi_movimento?anno_scolastico=2023/2024" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Via POST con JSON body
```bash
curl -X GET "http://localhost/next-api/src/v1/marketplace/verifica_tipi_movimento" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"anno_scolastico": "2023/2024"}'
```

### Via PHP Script
```bash
php src/v1/scripts/test_verifica_tipi_movimento_marketplace.php username password 2023/2024
```

## Risposta

### Struttura della risposta
```json
{
  "esito": "OK",
  "anno_scolastico_verificato": "2023/2024",
  "database_utilizzato": "mastercom_2023_2024",
  "totale_records_verificati": 15,
  "records_validi": [
    {
      "id_marketplace": 1,
      "descrizione": "Servizio Mensa",
      "id_tipo_movimento": 123,
      "school_year_tipo": "2023/2024",
      "ordinamento": 1,
      "confronto": "uguale"
    },
    {
      "id_marketplace": 4,
      "descrizione": "Servizio Libri",
      "id_tipo_movimento": 789,
      "school_year_tipo": "2024/2025",
      "ordinamento": 4,
      "confronto": "maggiore"
    }
  ],
  "records_non_validi": [
    {
      "id_marketplace": 2,
      "descrizione": "Servizio Trasporto",
      "id_tipo_movimento": 456,
      "school_year_tipo": "2022/2023",
      "anno_scolastico_verificato": "2023/2024",
      "ordinamento": 2,
      "motivo": "Anno scolastico del tipo movimento è precedente a quello da verificare"
    }
  ],
  "errori_chiamate_mc2": [
    {
      "id_marketplace": 3,
      "descrizione": "Servizio Libri",
      "id_tipo_movimento": 789,
      "ordinamento": 3,
      "motivo": "Errore nella chiamata MC2 o tipo movimento non trovato",
      "risposta_mc2": "Request Error: Connection timeout"
    }
  ],
  "conteggi": {
    "validi": 1,
    "non_validi": 1,
    "errori": 1
  },
  "messaggio": "Verifica completata con successo"
}
```

### Risposta di errore (parametro mancante)
```json
{
  "esito": "KO",
  "dati": [],
  "messaggio": "Parametro anno_scolastico obbligatorio non fornito. Formato richiesto: YYYY/YYYY (es. 2023/2024)"
}
```

### Campi della risposta

#### Campi principali
- `esito`: "OK" se la verifica è stata completata, "KO" in caso di errore
- `anno_scolastico_verificato`: Anno scolastico specificato per la verifica
- `database_utilizzato`: Nome del database utilizzato per la verifica
- `totale_records_verificati`: Numero totale di record marketplace verificati
- `messaggio`: Messaggio descrittivo del risultato

#### Records validi
Array di record marketplace che hanno un tipo movimento con school_year uguale o maggiore all'anno verificato:
- `id_marketplace`: ID del record marketplace
- `descrizione`: Descrizione del servizio
- `id_tipo_movimento`: ID del tipo movimento
- `school_year_tipo`: Anno scolastico del tipo movimento
- `ordinamento`: Ordinamento del record
- `confronto`: "uguale" se gli anni sono uguali, "maggiore" se il tipo movimento è di un anno successivo

#### Records non validi
Array di record marketplace che hanno un tipo movimento con school_year precedente all'anno verificato:
- Tutti i campi dei record validi (eccetto `confronto`) più:
- `anno_scolastico_verificato`: Anno scolastico verificato per confronto
- `motivo`: Spiegazione del problema

#### Errori chiamate MC2
Array di record per cui la chiamata all'API MC2 ha fallito:
- `id_marketplace`, `descrizione`, `id_tipo_movimento`, `ordinamento`: Dati del record
- `motivo`: Tipo di errore
- `risposta_mc2`: Risposta ricevuta dall'API MC2

#### Conteggi
Riepilogo numerico dei risultati:
- `validi`: Numero di record validi
- `non_validi`: Numero di record non validi
- `errori`: Numero di errori nelle chiamate MC2

## Logica di funzionamento

1. **Validazione parametri**: L'API verifica che il parametro `anno_scolastico` sia fornito nel formato corretto (YYYY/YYYY)

2. **Connessione database**: Si collega al database specifico dell'anno scolastico (es. `mastercom_2023_2024`)

3. **Estrazione records**: Estrae tutti i record dalla tabella `marketplace` dove:
   - `flag_canc = 0` (non cancellati)
   - `id_tipo_movimento IS NOT NULL AND id_tipo_movimento > 0` (con tipo movimento valorizzato)

4. **Verifica anno scolastico**: Per ogni record:
   - Chiama l'API MC2 con path `ccp/type/{id_tipo_movimento}` per ottenere i dettagli del tipo movimento
   - Confronta il campo `school_year` del tipo movimento con l'anno scolastico specificato

5. **Classificazione risultati**: I record vengono classificati in:
   - **Validi**: `school_year` del tipo movimento >= anno scolastico specificato
   - **Non validi**: `school_year` del tipo movimento < anno scolastico specificato
   - **Errori**: Chiamata MC2 fallita o tipo movimento non trovato

6. **Ripristino database**: Ripristina la connessione al database originale

## Casi d'uso

- **Controllo qualità dati**: Verificare che i servizi marketplace siano associati a tipi movimento dell'anno corrente
- **Migrazione dati**: Identificare record che necessitano aggiornamento dopo cambio anno scolastico
- **Debugging**: Individuare problemi di configurazione nei servizi marketplace
- **Audit**: Controllo periodico della coerenza dei dati

## Note tecniche

- L'API utilizza il metodo `callMc2` della classe `School` per comunicare con l'API MC2
- La verifica viene effettuata solo sui record con `id_tipo_movimento` valorizzato
- Gli errori di comunicazione con MC2 vengono tracciati separatamente dai record non validi
- L'API si collega automaticamente al database dell'anno scolastico specificato
- Il confronto degli anni scolastici considera validi anche gli anni successivi a quello specificato
- La connessione al database originale viene sempre ripristinata, anche in caso di errore
- Il formato dell'anno scolastico deve essere YYYY/YYYY (es. "2023/2024")
