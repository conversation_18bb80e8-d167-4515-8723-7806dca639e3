# API Verifica Tipi Movimento Marketplace

## Descrizione
Questa API verifica che i record di marketplace abbiano un `id_tipo_movimento` che corrisponde ad un tipo movimento che ha come `school_year` l'anno del database attuale del mastercom.

## Endpoint
```
GET /v1/marketplace/verifica_tipi_movimento
```

## Parametri
- `db_richiesto` (opzionale): Nome del database specifico da utilizzare

## Esempio di chiamata

### Via cURL
```bash
curl -X GET "http://localhost/next-api/src/v1/marketplace/verifica_tipi_movimento" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Con parametro db_richiesto
```bash
curl -X GET "http://localhost/next-api/src/v1/marketplace/verifica_tipi_movimento?db_richiesto=mastercom_2023_2024" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Via PHP Script
```bash
php src/v1/scripts/test_verifica_tipi_movimento_marketplace.php username password [db_richiesto]
```

## Risposta

### Struttura della risposta
```json
{
  "esito": "OK",
  "anno_scolastico_attuale": "2023/2024",
  "totale_records_verificati": 15,
  "records_validi": [
    {
      "id_marketplace": 1,
      "descrizione": "Servizio Mensa",
      "id_tipo_movimento": 123,
      "school_year_tipo": "2023/2024",
      "ordinamento": 1
    }
  ],
  "records_non_validi": [
    {
      "id_marketplace": 2,
      "descrizione": "Servizio Trasporto",
      "id_tipo_movimento": 456,
      "school_year_tipo": "2022/2023",
      "school_year_attuale": "2023/2024",
      "ordinamento": 2,
      "motivo": "Anno scolastico del tipo movimento non corrisponde a quello attuale"
    }
  ],
  "errori_chiamate_mc2": [
    {
      "id_marketplace": 3,
      "descrizione": "Servizio Libri",
      "id_tipo_movimento": 789,
      "ordinamento": 3,
      "motivo": "Errore nella chiamata MC2 o tipo movimento non trovato",
      "risposta_mc2": "Request Error: Connection timeout"
    }
  ],
  "conteggi": {
    "validi": 1,
    "non_validi": 1,
    "errori": 1
  },
  "messaggio": "Verifica completata con successo"
}
```

### Campi della risposta

#### Campi principali
- `esito`: "OK" se la verifica è stata completata, "KO" in caso di errore
- `anno_scolastico_attuale`: Anno scolastico attuale del database mastercom
- `totale_records_verificati`: Numero totale di record marketplace verificati
- `messaggio`: Messaggio descrittivo del risultato

#### Records validi
Array di record marketplace che hanno un tipo movimento con school_year corrispondente all'anno attuale:
- `id_marketplace`: ID del record marketplace
- `descrizione`: Descrizione del servizio
- `id_tipo_movimento`: ID del tipo movimento
- `school_year_tipo`: Anno scolastico del tipo movimento
- `ordinamento`: Ordinamento del record

#### Records non validi
Array di record marketplace che hanno un tipo movimento con school_year diverso dall'anno attuale:
- Tutti i campi dei record validi più:
- `school_year_attuale`: Anno scolastico attuale per confronto
- `motivo`: Spiegazione del problema

#### Errori chiamate MC2
Array di record per cui la chiamata all'API MC2 ha fallito:
- `id_marketplace`, `descrizione`, `id_tipo_movimento`, `ordinamento`: Dati del record
- `motivo`: Tipo di errore
- `risposta_mc2`: Risposta ricevuta dall'API MC2

#### Conteggi
Riepilogo numerico dei risultati:
- `validi`: Numero di record validi
- `non_validi`: Numero di record non validi
- `errori`: Numero di errori nelle chiamate MC2

## Logica di funzionamento

1. **Estrazione records**: L'API estrae tutti i record dalla tabella `marketplace` dove:
   - `flag_canc = 0` (non cancellati)
   - `id_tipo_movimento IS NOT NULL AND id_tipo_movimento > 0` (con tipo movimento valorizzato)

2. **Verifica anno scolastico**: Per ogni record:
   - Ottiene l'anno scolastico attuale dal parametro `ANNO_SCOLASTICO_ATTUALE`
   - Chiama l'API MC2 con path `ccp/type/{id_tipo_movimento}` per ottenere i dettagli del tipo movimento
   - Confronta il campo `school_year` del tipo movimento con l'anno scolastico attuale

3. **Classificazione risultati**: I record vengono classificati in:
   - **Validi**: `school_year` del tipo movimento = anno scolastico attuale
   - **Non validi**: `school_year` del tipo movimento ≠ anno scolastico attuale
   - **Errori**: Chiamata MC2 fallita o tipo movimento non trovato

## Casi d'uso

- **Controllo qualità dati**: Verificare che i servizi marketplace siano associati a tipi movimento dell'anno corrente
- **Migrazione dati**: Identificare record che necessitano aggiornamento dopo cambio anno scolastico
- **Debugging**: Individuare problemi di configurazione nei servizi marketplace
- **Audit**: Controllo periodico della coerenza dei dati

## Note tecniche

- L'API utilizza il metodo `callMc2` della classe `School` per comunicare con l'API MC2
- La verifica viene effettuata solo sui record con `id_tipo_movimento` valorizzato
- Gli errori di comunicazione con MC2 vengono tracciati separatamente dai record non validi
- L'anno scolastico viene recuperato dal parametro di sistema `ANNO_SCOLASTICO_ATTUALE`
